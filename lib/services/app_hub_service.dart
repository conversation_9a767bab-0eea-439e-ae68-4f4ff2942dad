import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class AppHubService {
  final AppState appState;
  final RpiCommandService commandService;

  AppHubRepository? _repository;
  final Map<String, AppInstallationStatus> _installationStatus = {};

  // Terminal output persistence
  final List<String> _terminalOutput = [];
  bool _isAnyInstallationInProgress = false;

  AppHubService(this.appState, this.commandService);

  AppHubRepository? get repository => _repository;
  Map<String, AppInstallationStatus> get installationStatus =>
      _installationStatus;
  List<String> get terminalOutput => List.unmodifiable(_terminalOutput);
  bool get isAnyInstallationInProgress => _isAnyInstallationInProgress;

  void addTerminalOutput(String output) {
    _terminalOutput.add(output);
  }

  void clearTerminalOutput() {
    _terminalOutput.clear();
  }

  /// Load the app repository from assets
  Future<void> loadRepository() async {
    debugPrint('Starting App Hub repository loading...');
    try {
      // Try to load from file system first (for development)
      String? repositoryData = await _loadRepositoryFromFileSystem();

      // Fallback to asset bundle if file system fails
      if (repositoryData == null) {
        try {
          debugPrint('Attempting to load repository from asset bundle...');
          repositoryData =
              await rootBundle.loadString('assets/app_hub/repository.json');
          debugPrint('Loaded App Hub repository from asset bundle');
        } catch (e) {
          debugPrint('Could not load repository from assets: $e');
          throw Exception(
              'Failed to load App Hub repository from both file system and assets');
        }
      } else {
        debugPrint('Loaded App Hub repository from file system');
      }

      debugPrint('Parsing JSON data...');
      final jsonData = jsonDecode(repositoryData) as Map<String, dynamic>;
      _repository = AppHubRepository.fromJson(jsonData);
      debugPrint(
          'Successfully loaded App Hub repository with ${_repository!.applications.length} applications');
      debugPrint('Repository name: ${_repository!.repository.name}');
      debugPrint('Categories: ${_repository!.categories.length}');
    } catch (e) {
      debugPrint('Error loading app repository: $e');
      rethrow;
    }
  }

  /// Load repository from file system (for development)
  Future<String?> _loadRepositoryFromFileSystem() async {
    try {
      // Get the project root directory
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }

      final repositoryFile =
          File('$projectRoot/assets/app_hub/repository.json');
      if (await repositoryFile.exists()) {
        return await repositoryFile.readAsString();
      }
      return null;
    } catch (e) {
      debugPrint('Error loading repository from file system: $e');
      return null;
    }
  }

  /// Check installation status for all apps
  Future<void> checkInstallationStatus(String deviceId) async {
    if (_repository == null) return;

    _installationStatus.clear();

    for (final app in _repository!.applications) {
      _installationStatus[app.id] = AppInstallationStatus.checking;

      try {
        final result = await commandService.executeCommand(
          deviceId,
          app.checkInstalled,
          showProgress: false,
        );

        _installationStatus[app.id] = result.success
            ? AppInstallationStatus.installed
            : AppInstallationStatus.notInstalled;
      } catch (e) {
        _installationStatus[app.id] = AppInstallationStatus.unknown;
      }
    }
  }

  /// Install an application
  Future<bool> installApplication(
      String deviceId, AppHubApplication app) async {
    return installApplicationWithOutput(deviceId, app);
  }

  /// Install an application with terminal output
  Future<bool> installApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput, Function(bool)? onRebootRequired}) async {
    // Check if any installation is already in progress
    if (_isAnyInstallationInProgress) {
      onOutput?.call(
          '❌ Another installation is already in progress. Please wait for it to complete.');
      return false;
    }

    _isAnyInstallationInProgress = true;
    _installationStatus[app.id] = AppInstallationStatus.installing;

    try {
      final startMessage = '╭─ Starting installation of ${app.name}';
      onOutput?.call(startMessage);
      addTerminalOutput(startMessage);

      final separator = '│';
      onOutput?.call(separator);
      addTerminalOutput(separator);

      // Check dependencies first
      if (app.dependencies.isNotEmpty) {
        onOutput?.call('├─ Checking dependencies...');
        for (final dep in app.dependencies) {
          final depApp = _repository?.applications.firstWhere(
            (a) => a.id == dep,
            orElse: () => throw Exception('Dependency $dep not found'),
          );

          if (depApp != null) {
            final depStatus = _installationStatus[dep];
            if (depStatus != AppInstallationStatus.installed) {
              onOutput?.call('│  ├─ Installing dependency: $dep');
              // Install dependency first
              final depInstalled = await installApplicationWithOutput(
                  deviceId, depApp,
                  onOutput: onOutput, onRebootRequired: onRebootRequired);
              if (!depInstalled) {
                throw Exception('Failed to install dependency: $dep');
              }
              onOutput?.call('│  └─ Dependency $dep installed successfully');
            } else {
              onOutput?.call('│  ✓ Dependency $dep already installed');
            }
          }
        }
        onOutput?.call('│');
      }

      // Execute installation commands
      onOutput?.call('├─ Executing installation commands...');
      for (int i = 0; i < app.installCommands.length; i++) {
        final command = app.installCommands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${app.installCommands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Installing ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 30),
        );

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Installation failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Verify installation
      onOutput?.call('│');
      onOutput?.call('├─ Verifying installation...');
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (verifyResult.success) {
        onOutput?.call('│  ✅ Installation verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} installed successfully');

        // Check if reboot is required
        if (app.requiresReboot) {
          onOutput?.call('');
          onOutput?.call(
              '⚠️  This application requires a system reboot to function properly.');
          onRebootRequired?.call(true);
        }

        _installationStatus[app.id] = AppInstallationStatus.installed;
        return true;
      } else {
        final failMessage = '│  ❌ Installation verification failed';
        onOutput?.call(failMessage);
        addTerminalOutput(failMessage);

        final endMessage = '╰─ ❌ Installation verification failed';
        onOutput?.call('│');
        onOutput?.call(endMessage);
        addTerminalOutput('│');
        addTerminalOutput(endMessage);

        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      final errorMessage = '╰─ ❌ Installation failed: $e';
      onOutput?.call('│');
      onOutput?.call(errorMessage);
      addTerminalOutput('│');
      addTerminalOutput(errorMessage);

      debugPrint('Error installing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Remove an application
  Future<bool> removeApplication(String deviceId, AppHubApplication app) async {
    return removeApplicationWithOutput(deviceId, app);
  }

  /// Remove an application with terminal output
  Future<bool> removeApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput}) async {
    // Check if any installation is already in progress
    if (_isAnyInstallationInProgress) {
      onOutput?.call(
          '❌ Another operation is already in progress. Please wait for it to complete.');
      return false;
    }

    _isAnyInstallationInProgress = true;
    _installationStatus[app.id] = AppInstallationStatus.removing;

    try {
      final startMessage = '╭─ Starting removal of ${app.name}';
      onOutput?.call(startMessage);
      addTerminalOutput(startMessage);

      final separator = '│';
      onOutput?.call(separator);
      addTerminalOutput(separator);

      // Execute removal commands
      onOutput?.call('├─ Executing removal commands...');
      for (int i = 0; i < app.removeCommands.length; i++) {
        final command = app.removeCommands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${app.removeCommands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Removing ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 15),
        );

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Removal failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Verify removal
      onOutput?.call('│');
      onOutput?.call('├─ Verifying removal...');
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (!verifyResult.success) {
        onOutput?.call('│  ✅ Removal verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} removed successfully');
        _installationStatus[app.id] = AppInstallationStatus.notInstalled;
        return true;
      } else {
        onOutput?.call('│  ❌ Removal verification failed');
        onOutput?.call('│');
        onOutput?.call('╰─ ❌ Removal verification failed');
        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      onOutput?.call('│');
      onOutput?.call('╰─ ❌ Removal failed: $e');
      debugPrint('Error removing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Update an application
  Future<bool> updateApplication(String deviceId, AppHubApplication app) async {
    return updateApplicationWithOutput(deviceId, app);
  }

  /// Update an application with terminal output
  Future<bool> updateApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput, Function(bool)? onRebootRequired}) async {
    // Check if any installation is already in progress
    if (_isAnyInstallationInProgress) {
      onOutput?.call(
          '❌ Another operation is already in progress. Please wait for it to complete.');
      return false;
    }

    _isAnyInstallationInProgress = true;
    _installationStatus[app.id] = AppInstallationStatus.updating;

    try {
      final startMessage = '╭─ Starting update of ${app.name}';
      onOutput?.call(startMessage);
      addTerminalOutput(startMessage);

      final separator = '│';
      onOutput?.call(separator);
      addTerminalOutput(separator);

      // Use update commands if available, otherwise fall back to install commands
      final commands = app.updateCommands ?? app.installCommands;

      // Execute update commands
      onOutput?.call('├─ Executing update commands...');
      for (int i = 0; i < commands.length; i++) {
        final command = commands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${commands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Updating ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 30),
        );

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Update failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Verify installation
      onOutput?.call('│');
      onOutput?.call('├─ Verifying update...');
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (verifyResult.success) {
        onOutput?.call('│  ✅ Update verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} updated successfully');

        // Check if reboot is required
        if (app.requiresReboot) {
          onOutput?.call('');
          onOutput?.call(
              '⚠️  This application requires a system reboot to function properly.');
          onRebootRequired?.call(true);
        }

        _installationStatus[app.id] = AppInstallationStatus.installed;
        return true;
      } else {
        onOutput?.call('│  ❌ Update verification failed');
        onOutput?.call('│');
        onOutput?.call('╰─ ❌ Update verification failed');
        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      onOutput?.call('│');
      onOutput?.call('╰─ ❌ Update failed: $e');
      debugPrint('Error updating ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Get applications by category
  List<AppHubApplication> getApplicationsByCategory(String categoryId) {
    if (_repository == null) return [];
    return _repository!.applications
        .where((app) => app.category == categoryId)
        .toList();
  }

  /// Search applications
  List<AppHubApplication> searchApplications(String query) {
    if (_repository == null) return [];
    final lowerQuery = query.toLowerCase();
    return _repository!.applications.where((app) {
      return app.name.toLowerCase().contains(lowerQuery) ||
          app.description.toLowerCase().contains(lowerQuery) ||
          app.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }
}

enum AppInstallationStatus {
  unknown,
  checking,
  notInstalled,
  installed,
  installing,
  removing,
  failed,
  updateAvailable,
  updating,
}

class AppHubRepository {
  final RepositoryInfo repository;
  final List<AppCategory> categories;
  final List<AppHubApplication> applications;

  AppHubRepository({
    required this.repository,
    required this.categories,
    required this.applications,
  });

  factory AppHubRepository.fromJson(Map<String, dynamic> json) {
    return AppHubRepository(
      repository: RepositoryInfo.fromJson(json['repository']),
      categories: (json['categories'] as List)
          .map((cat) => AppCategory.fromJson(cat))
          .toList(),
      applications: (json['applications'] as List)
          .map((app) => AppHubApplication.fromJson(app))
          .toList(),
    );
  }
}

class RepositoryInfo {
  final String name;
  final String version;
  final String description;
  final DateTime lastUpdated;

  RepositoryInfo({
    required this.name,
    required this.version,
    required this.description,
    required this.lastUpdated,
  });

  factory RepositoryInfo.fromJson(Map<String, dynamic> json) {
    return RepositoryInfo(
      name: json['name'],
      version: json['version'],
      description: json['description'],
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }
}

class AppCategory {
  final String id;
  final String name;
  final String description;
  final String icon;

  AppCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
  });

  factory AppCategory.fromJson(Map<String, dynamic> json) {
    return AppCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
    );
  }
}

class AppHubApplication {
  final String id;
  final String name;
  final String description;
  final String version;
  final String category;
  final String icon;
  final String size;
  final String installType;
  final List<String> installCommands;
  final List<String> removeCommands;
  final String checkInstalled;
  final String? checkVersion;
  final List<String>? updateCommands;
  final List<String> tags;
  final bool requiresReboot;
  final bool piOnly;
  final List<String> dependencies;

  AppHubApplication({
    required this.id,
    required this.name,
    required this.description,
    required this.version,
    required this.category,
    required this.icon,
    required this.size,
    required this.installType,
    required this.installCommands,
    required this.removeCommands,
    required this.checkInstalled,
    this.checkVersion,
    this.updateCommands,
    required this.tags,
    required this.requiresReboot,
    required this.piOnly,
    this.dependencies = const [],
  });

  factory AppHubApplication.fromJson(Map<String, dynamic> json) {
    return AppHubApplication(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      version: json['version'],
      category: json['category'],
      icon: json['icon'],
      size: json['size'],
      installType: json['install_type'],
      installCommands: List<String>.from(json['install_commands']),
      removeCommands: List<String>.from(json['remove_commands']),
      checkInstalled: json['check_installed'],
      checkVersion: json['check_version'],
      updateCommands: json['update_commands'] != null
          ? List<String>.from(json['update_commands'])
          : null,
      tags: List<String>.from(json['tags']),
      requiresReboot: json['requires_reboot'] ?? false,
      piOnly: json['pi_only'] ?? false,
      dependencies: List<String>.from(json['dependencies'] ?? []),
    );
  }
}
